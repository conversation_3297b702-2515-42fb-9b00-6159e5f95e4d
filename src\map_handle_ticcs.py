import re
import os
from pathlib import Path


class TiCcsMapFileParser:
    """Ti CCS MAP文件解析器"""

    def __init__(self, map_file_path):
        self.map_file_path = Path(map_file_path)
        self.sections = {}
        self.symbols = {}
        self.memory_config = {}
        self.memory_usage = {}
        self.module_usage = {}
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0
        self.segment_allocation = {}

    def parse_map_file(self):
        """解析Ti CCS MAP文件"""
        if not self.map_file_path.exists():
            raise FileNotFoundError(f"MAP文件不存在: {self.map_file_path}")

        with open(self.map_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        self._parse_memory_configuration(content)
        self._parse_segment_allocation_map(content)
        self._parse_section_allocation_map(content)
        self._parse_global_symbols(content)
        self._calculate_sizes()
        self._calculate_memory_usage(content)
        self._calculate_module_usage()

    def _parse_memory_configuration(self, content):
        """解析MEMORY CONFIGURATION部分"""
        # 查找MEMORY CONFIGURATION部分
        mem_config_match = re.search(r'MEMORY CONFIGURATION\s*\n.*?name\s+origin\s+length\s+used\s+unused\s+attr\s+fill\s*\n.*?-+.*?\n(.*?)(?=\n\s*\nSEGMENT ALLOCATION MAP)', content, re.DOTALL)

        if not mem_config_match:
            return

        mem_config_text = mem_config_match.group(1)

        # 解析每个memory段
        # 格式: name origin length used unused attr fill
        for line in mem_config_text.split('\n'):
            line = line.strip()
            if not line:
                continue

            # 匹配内存配置行，处理可能的空格对齐
            parts = line.split()
            if len(parts) >= 5:  # 至少需要name, origin, length, used, unused
                name = parts[0]
                try:
                    origin = int(parts[1], 16)
                    length = int(parts[2], 16)
                    used = int(parts[3], 16)
                    unused = int(parts[4], 16)
                    attributes = parts[5] if len(parts) > 5 else ""

                    self.memory_config[name] = {
                        'origin': origin,
                        'length': length,
                        'used': used,
                        'unused': unused,
                        'attributes': attributes,
                        'end': origin + length
                    }
                except ValueError:
                    continue

    def _parse_segment_allocation_map(self, content):
        """解析SEGMENT ALLOCATION MAP部分"""
        # 查找SEGMENT ALLOCATION MAP部分
        segment_match = re.search(r'SEGMENT ALLOCATION MAP\s*\n.*?\n.*?\n(.*?)(?=\n\s*\nSECTION ALLOCATION MAP)', content, re.DOTALL)

        if not segment_match:
            return

        segment_text = segment_match.group(1)

        # 解析段分配信息
        # 格式: run origin  load origin   length   init length attrs members
        for line in segment_text.split('\n'):
            line = line.strip()
            if not line or line.startswith('-'):
                continue

            # 匹配段信息行
            parts = line.split()
            if len(parts) >= 5:
                try:
                    run_origin = int(parts[0], 16)
                    load_origin = int(parts[1], 16)
                    length = int(parts[2], 16)
                    init_length = int(parts[3], 16)
                    attrs = parts[4] if len(parts) > 4 else ""

                    segment_key = f"{run_origin:08x}"
                    self.segment_allocation[segment_key] = {
                        'run_origin': run_origin,
                        'load_origin': load_origin,
                        'length': length,
                        'init_length': init_length,
                        'attributes': attrs
                    }
                except ValueError:
                    continue

    def _parse_section_allocation_map(self, content):
        """解析SECTION ALLOCATION MAP部分"""
        # 查找SECTION ALLOCATION MAP部分，更精确地定位结束位置
        section_match = re.search(r'SECTION ALLOCATION MAP\s*\n.*?\n.*?\n.*?\n(.*?)(?=\n\s*\n(?:GLOBAL SYMBOLS|ENTRY POINT|STACK USAGE|LINKER GENERATED COPY TABLES|FAR CALL TRAMPOLINES|MODULE SUMMARY))', content, re.DOTALL)

        if not section_match:
            return

        section_text = section_match.group(1)

        # 解析段信息
        current_section = None
        for line in section_text.split('\n'):
            line_stripped = line.strip()
            if not line_stripped:
                continue

            # 检查是否是新的段定义
            # Ti CCS格式: .section_name 或者 .section_name (后面可能有空格)
            if line_stripped.startswith('.') and not line.startswith(' ') and not line.startswith('\t'):
                # 新段开始，格式可能是: .section_name 或者 .section_name page address size
                section_name = line_stripped.split()[0]
                current_section = section_name

                # 尝试解析完整的段信息（如果有的话）
                parts = line_stripped.split()
                if len(parts) >= 4:
                    try:
                        page = int(parts[1])
                        address = int(parts[2], 16)
                        size = int(parts[3], 16)
                    except (ValueError, IndexError):
                        # 如果解析失败，使用默认值
                        page = 0
                        address = 0
                        size = 0
                else:
                    page = 0
                    address = 0
                    size = 0

                # 使用统一的段类型分类器
                classified_type = self._classify_section_type(section_name)

                if classified_type == 'text':
                    section_type = 'PROGBITS'
                    category = 'text'
                elif classified_type == 'data':
                    section_type = 'PROGBITS'
                    category = 'data'
                elif classified_type == 'bss':
                    section_type = 'NOBITS'
                    category = 'bss'
                elif classified_type == 'skip':
                    continue
                else:
                    section_type = 'PROGBITS'
                    category = 'other'

                self.sections[section_name] = {
                    'address': address,
                    'size': size,
                    'type': section_type,
                    'category': category,
                    'page': page
                }
            elif line.startswith(' ') or line.startswith('\t'):
                # 段内的详细信息，包含模块信息
                # Ti CCS格式: address size [library : file (.section)]
                detail_match = re.match(r'\s+([0-9a-f]+)\s+([0-9a-f]+)\s+(.+)', line)
                if detail_match and current_section:
                    try:
                        address = int(detail_match.group(1), 16)
                        size = int(detail_match.group(2), 16)
                        file_info = detail_match.group(3).strip()

                        if size == 0:
                            continue

                        # 解析模块名称
                        module_name = self._extract_module_name(file_info)
                        if module_name and module_name != '--HOLE--':
                            # 只统计库文件（.a 和 .lib），不统计单个 .o 文件
                            if not self._is_library_file(module_name):
                                continue

                            # 根据段名确定类型
                            section_type = self._classify_section_type(current_section)

                            # 跳过不需要统计的段
                            if section_type == 'skip':
                                continue

                            # 统计模块使用情况
                            if module_name not in self.module_usage:
                                self.module_usage[module_name] = {
                                    'text': 0, 'data': 0, 'bss': 0, 'total': 0
                                }

                            # 归类到text/data/bss
                            if section_type not in ['text', 'data', 'bss']:
                                section_type = 'data'

                            self.module_usage[module_name][section_type] += size
                            self.module_usage[module_name]['total'] += size
                    except ValueError:
                        # 如果地址或大小解析失败，跳过这一行
                        continue

    def _parse_global_symbols(self, content):
        """解析GLOBAL SYMBOLS部分"""
        # 查找GLOBAL SYMBOLS部分
        symbols_match = re.search(r'GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name\s*\n.*?\n.*?\n(.*?)$', content, re.DOTALL)

        if not symbols_match:
            return

        symbols_text = symbols_match.group(1)

        # 解析符号信息
        # 格式: address   name
        for line in symbols_text.split('\n'):
            line = line.strip()
            if not line:
                continue

            # 匹配符号行
            symbol_match = re.match(r'^([0-9a-f]+)\s+(.+)', line)
            if symbol_match:
                address = int(symbol_match.group(1), 16)
                symbol_name = symbol_match.group(2).strip()

                self.symbols[symbol_name] = {
                    'address': address,
                    'size': 0,  # Ti CCS map文件中符号没有大小信息
                    'type': 'T',  # 默认为Text类型
                    'file': 'unknown'
                }

    def _extract_module_name(self, file_info):
        """从文件信息中提取模块名称"""
        if not file_info:
            return None

        # 处理Ti CCS格式的文件信息
        # 格式可能是: library.a : file.obj (.section)
        # 或者: file.obj (.section)
        # 或者: : file.obj (.section) (前面省略了库名)
        # 或者: 41c00150   zx_microsar.lib : Os_Hal_EntryAsm_Lcfg.o (.OS_EXCVEC_CORE0_CODE)

        # 首先检查是否以地址开头（8位十六进制数字 + 空格）
        # 如果是，则跳过地址部分
        address_match = re.match(r'^([0-9a-f]{8})\s+(.+)', file_info)
        if address_match:
            file_info = address_match.group(2)  # 跳过地址部分

        # 移除段信息 (.section)
        file_info = re.sub(r'\s*\([^)]+\)\s*$', '', file_info)

        # 检查是否包含库文件信息
        if ':' in file_info:
            parts = file_info.split(':', 1)  # 只分割第一个冒号
            if len(parts) >= 2:
                lib_name = parts[0].strip()
                obj_name = parts[1].strip()

                # 如果库名不为空且不是特殊标记，优先使用库名
                if lib_name and not lib_name.startswith('--') and lib_name != '':
                    return lib_name
                else:
                    return obj_name

        # 直接返回文件名
        return file_info.strip()

    def _is_library_file(self, module_name):
        """判断是否为库文件（.a 或 .lib）"""
        if not module_name:
            return False

        module_lower = module_name.lower()
        return module_lower.endswith('.a') or module_lower.endswith('.lib')

    def _classify_section_type(self, section_name):
        """分类段类型 - 兼容ARM GCC的分类逻辑"""
        if not section_name:
            return 'skip'

        section_lower = section_name.lower()

        # 调试段和其他不需要统计的段
        debug_patterns = [
            'debug', 'comment', 'note', 'stab', 'dwarf', 'line',
            'frame', 'eh_frame', 'gcc_except_table', 'jcr',
            'got', 'plt', 'rel', 'rela', 'hash', 'dynsym',
            'dynstr', 'interp', 'dynamic', 'shstrtab', 'symtab',
            'strtab', 'attributes', 'arm.exidx', 'arm.extab'
        ]

        for pattern in debug_patterns:
            if pattern in section_lower:
                return 'skip'

        # TEXT段（可执行代码段）
        if section_name.startswith('.text'):
            return 'text'

        # 检查含有text的段
        if 'text' in section_lower:
            return 'text'

        # 检查含有code的段
        code_patterns = ['_code', '.code', 'code_', 'code.']
        for pattern in code_patterns:
            if pattern in section_lower:
                return 'text'

        # BSS段（未初始化数据段）
        bss_patterns = [
            'bss', 'stack', 'heap', 'noinit', 'zero_init', 'common', 'sysmem'
        ]

        for pattern in bss_patterns:
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                pattern in section_lower):
                return 'bss'

        # DATA段（已初始化数据段）
        data_patterns = [
            'data', 'rodata', 'init_array', 'fini_array', 'ctors', 'dtors',
            'const', 'cinit', 'boardcfg_data'
        ]

        for pattern in data_patterns:
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                pattern in section_lower):
                return 'data'

        # 默认归类为data
        return 'data'

    def _calculate_sizes(self):
        """计算各段大小"""
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0

        for _, section_info in self.sections.items():
            size = section_info['size']
            category = section_info['category']

            if category == 'text':
                self.text_size += size
            elif category == 'data':
                self.data_size += size
            elif category == 'bss':
                self.bss_size += size

    def _calculate_memory_usage(self, _content):
        """计算内存使用情况"""
        # Ti CCS已经在MEMORY CONFIGURATION中提供了used信息
        for name, config in self.memory_config.items():
            self.memory_usage[name] = {
                'used': config['used'],
                'total': config['length'],
                'free': config['unused'],
                'usage_percent': (config['used'] / config['length'] * 100) if config['length'] > 0 else 0
            }

    def _calculate_module_usage(self):
        """计算模块使用情况 - 已在_parse_section_allocation_map中完成"""
        pass

    def get_memory_usage(self):
        """获取内存使用信息"""
        return self.text_size, self.data_size, self.bss_size, 0

    def get_memory_usage_info(self):
        """获取详细的内存使用信息"""
        info = []
        total_text = self.text_size
        total_data = self.data_size
        total_bss = self.bss_size
        total_all = total_text + total_data + total_bss

        info.append(f"代码段 (TEXT): {total_text:,} bytes ({total_text/1024:.1f} KB)")
        info.append(f"数据段 (DATA): {total_data:,} bytes ({total_data/1024:.1f} KB)")
        info.append(f"BSS段 (BSS):   {total_bss:,} bytes ({total_bss/1024:.1f} KB)")
        info.append(f"总计:          {total_all:,} bytes ({total_all/1024:.1f} KB)")

        return '\n'.join(info)

    def create_memory_bar(self, text, data, bss, other=0):
        """
        创建内存占用可视化条
        
        注意: 这个可视化图基于ELF段统计，确保了与实际内存布局的一致性
        """
        total = text + data + bss + other
        if total == 0:
            return "无法创建内存占用图"

        width = 50
        text_width = int(width * text / total)
        data_width = int(width * data / total)
        bss_width = int(width * bss / total)
        other_width = width - text_width - data_width - bss_width

        bar = "\n 内存占用可视化：\n\n"
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "text:", "█" * text_width, text, text/total*100, width=width)
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "data:", "█" * data_width, data, data/total*100, width=width)
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "bss:", "█" * bss_width, bss, bss/total*100, width=width)
        if other > 0:
            bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
                "other:", "█" * other_width, other, other/total*100, width=width)
        bar += "总计: {:,} bytes".format(total)
        return bar

    def get_module_usage_info(self):
        """获取模块使用信息"""
        if not self.module_usage:
            return "无模块使用信息"

        # 按总大小排序
        sorted_modules = sorted(self.module_usage.items(),
                              key=lambda x: x[1]['total'], reverse=True)

        info = []
        info.append("模块内存使用情况 (按总大小排序):")
        info.append("-" * 80)
        info.append(f"{'模块名':<30} {'TEXT':<10} {'DATA':<10} {'BSS':<10} {'总计':<10}")
        info.append("-" * 80)

        for module_name, usage in sorted_modules[:20]:  # 只显示前20个
            info.append(f"{module_name:<30} "
                       f"{usage['text']:<10} "
                       f"{usage['data']:<10} "
                       f"{usage['bss']:<10} "
                       f"{usage['total']:<10}")

        if len(sorted_modules) > 20:
            info.append(f"... 还有 {len(sorted_modules) - 20} 个模块")

        return '\n'.join(info)

    def search_symbol(self, symbol_name, use_regex=False):
        """搜索符号"""
        results = []

        if use_regex:
            try:
                pattern = re.compile(symbol_name, re.IGNORECASE)
                for name, info in self.symbols.items():
                    if pattern.search(name):
                        results.append({
                            'name': name,
                            'address': f"{info['address']:08x}",
                            'size': f"{info['size']:x}" if info['size'] > 0 else "",
                            'type': info['type'],
                            'file': info.get('file', 'unknown')
                        })
            except re.error:
                return []
        else:
            for name, info in self.symbols.items():
                if symbol_name.lower() in name.lower():
                    results.append({
                        'name': name,
                        'address': f"{info['address']:08x}",
                        'size': f"{info['size']:x}" if info['size'] > 0 else "",
                        'type': info['type'],
                        'file': info.get('file', 'unknown')
                    })

        return results

    def search_address(self, address):
        """通过地址搜索符号"""
        try:
            target_addr = int(address, 16)
        except ValueError:
            return None, None

        closest_symbol = None
        next_symbol = None

        # 按地址排序所有符号
        sorted_symbols = sorted(self.symbols.items(), key=lambda x: x[1]['address'])

        for name, info in sorted_symbols:
            if info['address'] <= target_addr:
                closest_symbol = (name, info)
            elif info['address'] > target_addr:
                next_symbol = (name, info)
                break

        return closest_symbol, next_symbol

    def get_memory_usage_info(self):
        """获取Memory Configuration使用信息"""
        if not self.memory_config:
            return "未找到Memory Configuration信息"

        info_lines = []
        info_lines.append("")

        # 表头
        header = f"{'Name':<24} {'Origin':<12} {'Length':<12} {'Used':<12} {'Usage %':<8} {'Attributes'}"
        info_lines.append(header)
        info_lines.append("-" * 80)

        # 按起始地址排序
        sorted_memory = sorted(self.memory_config.items(), key=lambda x: x[1]['origin'])

        for name, config in sorted_memory:
            origin_str = f"0x{config['origin']:08x}"
            length_str = f"0x{config['length']:08x}"
            used_str = f"0x{config['used']:08x}"
            usage_percent = (config['used'] / config['length'] * 100) if config['length'] > 0 else 0
            usage_str = f"{usage_percent:.1f}%"
            attributes = config.get('attributes', '')

            line = f"{name:<24} {origin_str:<12} {length_str:<12} {used_str:<12} {usage_str:<8} {attributes}"
            info_lines.append(line)

        # 添加总计信息
        total_length = sum(config['length'] for config in self.memory_config.values())
        total_used = sum(config['used'] for config in self.memory_config.values())
        total_usage_percent = (total_used / total_length * 100) if total_length > 0 else 0

        return '\n'.join(info_lines)

    def get_sections_info(self):
        """获取节区信息，模拟readelf -S的输出格式"""
        sections_info = []

        # 添加标题
        sections_info.append("Section Headers:")
        sections_info.append("")

        # 表头
        title_format = (
            f"  {'[Nr]':<6} "
            f"{'Type':<12} "
            f"{'Address':<16} "
            f"{'Size':<12} "
            f"{'Name'}"
        )
        sections_info.append(title_format)
        sections_info.append('-' * 80)

        # 节区信息
        nr = 0
        for section_name, section_info in self.sections.items():
            nr += 1
            formatted_line = (
                f"  [{nr:>2}] "
                f"{section_info.get('type', 'PROGBITS'):<12} "
                f"0x{section_info['address']:08x}    "
                f"0x{section_info['size']:08x} "
                f"{section_name}"
            )
            sections_info.append(formatted_line)

        return '\n'.join(sections_info)

    def get_section_classification_debug(self):
        """获取段分类调试信息"""
        debug_info = []
        debug_info.append("段分类详情:")
        debug_info.append("")

        # 按类型分组
        by_category = {}
        for section_name, section_info in self.sections.items():
            category = section_info['category']
            if category not in by_category:
                by_category[category] = []
            by_category[category].append((section_name, section_info))

        for category in ['text', 'data', 'bss', 'other']:
            if category in by_category:
                debug_info.append(f"{category.upper()} 段:")
                for section_name, section_info in by_category[category]:
                    size_kb = section_info['size'] / 1024
                    debug_info.append(f"  {section_name:<30} {section_info['size']:>8} bytes ({size_kb:>6.1f} KB)")
                debug_info.append("")

        return '\n'.join(debug_info)

    def get_module_usage_info(self):
        """获取模块使用信息"""
        info_lines = []
        info_lines.append("")

        # 表头
        header = f"{'Module':<30} {'Text':<10} {'Data':<10} {'BSS':<10} {'Total':<10} {'%':<6}"
        info_lines.append(header)
        info_lines.append("-" * 80)

        # 初始化累加变量
        module_text_total = 0
        module_data_total = 0
        module_bss_total = 0
        module_grand_total = 0

        # 如果有模块统计数据，显示模块级别的分解
        if self.module_usage:
            # 按总大小排序
            sorted_modules = sorted(self.module_usage.items(), key=lambda x: x[1]['total'], reverse=True)

            # 先计算所有模块的总计，用于百分比计算
            for module_name, usage in sorted_modules:
                module_text_total += usage['text']
                module_data_total += usage['data']
                module_bss_total += usage['bss']
                module_grand_total += usage['total']

            # 显示各个模块
            for module_name, usage in sorted_modules:
                percent = (usage['total'] / module_grand_total * 100) if module_grand_total > 0 else 0

                line = (f"{module_name:<30} "
                       f"{usage['text']:<10} "
                       f"{usage['data']:<10} "
                       f"{usage['bss']:<10} "
                       f"{usage['total']:<10} "
                       f"{percent:5.1f}%")
                info_lines.append(line)
        else:
            # 如果没有模块统计，使用主段统计作为备用
            text_total, data_total, bss_total, other_total = self.get_memory_usage()
            module_text_total = text_total
            module_data_total = data_total
            module_bss_total = bss_total
            module_grand_total = text_total + data_total + bss_total + other_total
            info_lines.append(f"{'[Main Sections]':<30} {module_text_total:<10} {module_data_total:<10} {module_bss_total:<10} {module_grand_total:<10} {'100.0%':<6}")

        info_lines.append("")

        return '\n'.join(info_lines)